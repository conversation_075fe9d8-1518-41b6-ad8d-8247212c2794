# from flask import Flask, request, jsonify
# from flask_socketio import So<PERSON><PERSON>, emit, join_room, leave_room
# from flask_cors import CORS
# import sys
# import os
# import json
# import uuid
# from datetime import datetime, timezone, timedelta
# import base64
# import asyncio
# import threading

# # LiveKit imports
# from livekit import api, rtc
# from livekit.api import AccessToken, VideoGrants, LiveKitAPI
# from livekit.rtc import Room, TrackSource

# # Add parent directory to path to import shared modules
# sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# from dotenv import load_dotenv
# from shared.database import Database  # Import Database

# # Import ProxyFix for handling reverse proxy headers
# from werkzeug.middleware.proxy_fix import ProxyFix

# load_dotenv()

# app = Flask(__name__)
# app.config['SECRET_KEY'] = 'enhanced-streaming-secret-key'

# # Configure CORS to allow all origins for development
# CORS(app,
#      origins="*",
#      allow_headers=["Content-Type", "Authorization", "Access-Control-Allow-Credentials"],
#      supports_credentials=True,
#      methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"])

# # Apply ProxyFix middleware to handle Nginx proxy headers
# app.wsgi_app = ProxyFix(app.wsgi_app, x_for=1, x_proto=1, x_host=1, x_port=1, x_prefix=1)

# # Initialize Socket.IO with CORS support for all origins
# # async_mode will be auto-detected (eventlet in production, threading in development)
# socketio = SocketIO(app,
#                    cors_allowed_origins="*",
#                    cors_credentials=True,
#                    logger=True,
#                    engineio_logger=True)

# # Initialize Database
# db = Database()

# # Add CORS preflight handler
# @app.before_request
# def handle_preflight():
#     if request.method == "OPTIONS":
#         response = jsonify({'status': 'OK'})
#         response.headers.add("Access-Control-Allow-Origin", "*")
#         response.headers.add('Access-Control-Allow-Headers', "*")
#         response.headers.add('Access-Control-Allow-Methods', "*")
#         return response

# # LiveKit Configuration from environment variables
# LIVEKIT_URL = os.getenv('LIVEKIT_URL')
# LIVEKIT_API_KEY = os.getenv('LIVEKIT_API_KEY')
# LIVEKIT_API_SECRET = os.getenv('LIVEKIT_API_SECRET')

# # Initialize LiveKit API Client - Lazy Initialization
# livekit_api = None

# def get_livekit_api():
#     """Get or create LiveKit API client"""
#     global livekit_api
#     if livekit_api is None:
#         livekit_api = LiveKitAPI(LIVEKIT_URL, LIVEKIT_API_KEY, LIVEKIT_API_SECRET)
#     return livekit_api

# # Global storage for enhanced streaming features
# enhanced_streams = {}
# stream_recordings = {}
# chat_messages = {}
# quality_settings = {}
# livekit_rooms = {}  # Track LiveKit rooms

# class LiveKitManager:
#     """LiveKit integration manager for room and token management"""

#     def __init__(self):
#         self.rooms = {}

#     def generate_access_token(self, room_name, participant_identity, participant_name=None, is_teacher=False):
#         """Generate LiveKit access token for a participant"""
#         try:
#             token = AccessToken(LIVEKIT_API_KEY, LIVEKIT_API_SECRET)
#             token.with_identity(participant_identity)
#             token.with_name(participant_name or participant_identity)

#             grants = VideoGrants(
#                 room_join=True,
#                 room=room_name,
#                 can_publish=is_teacher,
#                 can_subscribe=True,
#                 can_publish_data=True,
#                 can_update_own_metadata=True
#             )

#             if is_teacher:
#                 grants.room_admin = True

#             token.with_grants(grants)
#             token.with_ttl(timedelta(hours=24))
#             jwt_token = token.to_jwt()
#             print(f"✅ Generated LiveKit token for {participant_identity} in room {room_name}")
#             return jwt_token
#         except Exception as e:
#             print(f"❌ Error generating LiveKit token: {e}")
#             return None

#     async def create_room(self, room_name, max_participants=50):
#         """Create a LiveKit room"""
#         try:
#             room_request = api.CreateRoomRequest(
#                 name=room_name,
#                 max_participants=max_participants,
#                 empty_timeout=10 * 60,
#                 departure_timeout=60
#             )
#             api_client = get_livekit_api()
#             room = await api_client.room.create_room(room_request)
#             self.rooms[room_name] = {
#                 'room': room,
#                 'created_at': datetime.now(timezone.utc),
#                 'participants': [],
#                 'max_participants': max_participants
#             }
#             print(f"✅ Created LiveKit room: {room_name}")
#             return room
#         except Exception as e:
#             print(f"❌ Error creating LiveKit room {room_name}: {e}")
#             return None

#     async def delete_room(self, room_name):
#         """Delete a LiveKit room"""
#         try:
#             api_client = get_livekit_api()
#             await api_client.room.delete_room(api.DeleteRoomRequest(room=room_name))
#             if room_name in self.rooms:
#                 del self.rooms[room_name]
#             print(f"✅ Deleted LiveKit room: {room_name}")
#             return True
#         except Exception as e:
#             print(f"❌ Error deleting LiveKit room {room_name}: {e}")
#             return False

#     async def list_participants(self, room_name):
#         """List participants in a LiveKit room"""
#         try:
#             api_client = get_livekit_api()
#             response = await api_client.room.list_participants(api.ListParticipantsRequest(room=room_name))
#             return response.participants
#         except Exception as e:
#             print(f"❌ Error listing participants in room {room_name}: {e}")
#             return []

#     async def remove_participant(self, room_name, participant_identity):
#         """Remove a participant from a LiveKit room"""
#         try:
#             api_client = get_livekit_api()
#             await api_client.room.remove_participant(api.RoomParticipantIdentity(
#                 room=room_name,
#                 identity=participant_identity
#             ))
#             print(f"✅ Removed participant {participant_identity} from room {room_name}")
#             return True
#         except Exception as e:
#             print(f"❌ Error removing participant {participant_identity} from room {room_name}: {e}")
#             return False

# # Initialize LiveKit Manager
# livekit_manager = LiveKitManager()

# class EnhancedStreamManager:
#     def __init__(self):
#         self.streams = {}
#         self.connections = {}
#         self.recordings = {}

#     def create_enhanced_stream(self, teacher_id, session_id, socket_id, quality='medium'):
#         """Create an enhanced streaming session with LiveKit integration"""
#         stream_data = {
#             'teacher_id': teacher_id,
#             'session_id': session_id,
#             'teacher_socket': socket_id,
#             'viewers': [],
#             'created_at': datetime.now(timezone.utc),
#             'status': 'active',
#             'quality': quality,
#             'recording_enabled': True,
#             'chat_enabled': True,
#             'screen_sharing': True,
#             'viewer_count': 0,
#             'livekit_room': session_id,
#             'livekit_enabled': True
#         }
#         self.streams[session_id] = stream_data
#         chat_messages[session_id] = []
#         quality_settings[session_id] = {
#             'video_quality': quality,
#             'frame_rate': 30 if quality == 'high' else 20,
#             'resolution': '1920x1080' if quality == 'high' else '1280x720'
#         }

#         def create_livekit_room():
#             loop = asyncio.new_event_loop()
#             asyncio.set_event_loop(loop)
#             try:
#                 loop.run_until_complete(livekit_manager.create_room(session_id))
#             except Exception as e:
#                 print(f"❌ Error creating LiveKit room: {e}")
#             finally:
#                 loop.close()

#         threading.Thread(target=create_livekit_room, daemon=True).start()
#         print(f"✅ Enhanced stream with LiveKit created: {session_id} by teacher {teacher_id}")
#         return stream_data

#     def add_viewer(self, session_id, viewer_id, socket_id):
#         """Add a viewer to the enhanced stream"""
#         if session_id in self.streams:
#             viewer_data = {
#                 'viewer_id': viewer_id,
#                 'socket_id': socket_id,
#                 'joined_at': datetime.now(timezone.utc)
#             }
#             self.streams[session_id]['viewers'].append(viewer_data)
#             self.streams[session_id]['viewer_count'] = len(self.streams[session_id]['viewers'])
#             return True
#         return False

#     def remove_viewer(self, session_id, socket_id):
#         """Remove a viewer from the enhanced stream"""
#         if session_id in self.streams:
#             self.streams[session_id]['viewers'] = [
#                 v for v in self.streams[session_id]['viewers']
#                 if v['socket_id'] != socket_id
#             ]
#             self.streams[session_id]['viewer_count'] = len(self.streams[session_id]['viewers'])
#             return True
#         return False

#     def get_stream(self, session_id):
#         """Get enhanced stream information"""
#         return self.streams.get(session_id)

#     def stop_stream(self, session_id):
#         """Stop enhanced stream and save recording"""
#         print(f"🛑 Attempting to stop stream: {session_id}")
#         if session_id in self.streams:
#             stream = self.streams[session_id]
#             print(f"📊 Stream found - Teacher: {stream.get('teacher_id')}, Status: {stream.get('status')}, Viewers: {stream.get('viewer_count', 0)}")
#             stream['status'] = 'stopped'
#             stream['ended_at'] = datetime.now(timezone.utc)

#             if stream.get('recording_enabled'):
#                 duration = (datetime.now(timezone.utc) - stream['created_at']).total_seconds()
#                 self.recordings[session_id] = {
#                     'session_id': session_id,
#                     'teacher_id': stream['teacher_id'],
#                     'duration': duration,
#                     'quality': stream['quality'],
#                     'viewer_count': stream['viewer_count'],
#                     'chat_messages': len(chat_messages.get(session_id, [])),
#                     'recorded_at': datetime.now(timezone.utc)
#                 }
#                 print(f"💾 Recording saved - Duration: {duration:.1f}s, Quality: {stream['quality']}")

#             if stream.get('livekit_enabled'):
#                 def delete_livekit_room():
#                     loop = asyncio.new_event_loop()
#                     asyncio.set_event_loop(loop)
#                     try:
#                         loop.run_until_complete(livekit_manager.delete_room(session_id))
#                     except Exception as e:
#                         print(f"❌ Error deleting LiveKit room: {e}")
#                     finally:
#                         loop.close()
#                 threading.Thread(target=delete_livekit_room, daemon=True).start()

#             if session_id in chat_messages:
#                 del chat_messages[session_id]
#                 print(f"🧹 Cleaned up chat messages for session {session_id}")
#             if session_id in quality_settings:
#                 del quality_settings[session_id]
#                 print(f"🧹 Cleaned up quality settings for session {session_id}")

#             del self.streams[session_id]
#             print(f"✅ Stream {session_id} stopped and cleaned up successfully")
#             return True
#         else:
#             print(f"❌ Stream {session_id} not found in active streams")
#             print(f"📋 Active streams: {list(self.streams.keys())}")
#             return False

# enhanced_stream_manager = EnhancedStreamManager()

# def get_user_role_by_id(user_id):
#     """Helper function to get user role by ID from various tables."""
#     user = db.execute_query_one("SELECT role FROM users WHERE id = %s", (user_id,))
#     if user:
#         return user['role']
#     center = db.execute_query_one("SELECT 'center_counselor' as role FROM centers WHERE center_code = %s", (user_id,))
#     if center:
#         return center['role']
#     student = db.execute_query_one("SELECT 'student' as role FROM students WHERE id = %s", (user_id,))
#     if student:
#         return student['role']
#     parent = db.execute_query_one("SELECT 'parent' as role FROM parents WHERE id = %s", (user_id,))
#     if parent:
#         return parent['role']
#     faculty = db.execute_query_one("SELECT 'faculty' as role FROM faculty WHERE id = %s", (user_id,))
#     if faculty:
#         return faculty['role']
#     teacher = db.execute_query_one("SELECT 'kota_teacher' as role FROM kota_teachers WHERE id = %s", (user_id,))
#     if teacher:
#         return teacher['role']
#     return None

# @app.route('/health', methods=['GET'])
# def health_check():
#     """Enhanced streaming service health check with LiveKit status"""
#     livekit_status = 'configured' if all([LIVEKIT_URL, LIVEKIT_API_KEY, LIVEKIT_API_SECRET]) else 'not configured'
#     return jsonify({
#         'status': 'healthy',
#         'service': 'Enhanced Real-time Streaming Service with LiveKit',
#         'port': 8012,  # Updated to match the running port
#         'features': ['LiveKit', 'WebRTC', 'Socket.IO', 'Chat', 'Recording', 'Quality Controls'],
#         'active_streams': len(enhanced_stream_manager.streams),
#         'livekit': {
#             'status': livekit_status,
#             'url': LIVEKIT_URL,
#             'api_key': LIVEKIT_API_KEY[:8] + '...' if LIVEKIT_API_KEY else None,
#             'rooms_managed': len(livekit_manager.rooms)
#         },
#         'timestamp': datetime.now().isoformat()
#     }), 200

# # HTTP-based Chat API Endpoints
# @app.route('/api/chat/send', methods=['POST'])
# def send_chat_message():
#     """Send a chat message via HTTP"""
#     try:
#         data = request.get_json()
#         session_id = data.get('session_id')
#         message = data.get('message')
#         sender_id = data.get('sender_id')
#         sender_name = data.get('sender_name', 'Anonymous')

#         if not session_id or not message:
#             return jsonify({'message': 'session_id and message are required'}), 400

#         # Get sender role
#         sender_role = get_user_role_by_id(sender_id) or 'unknown'

#         # Initialize chat messages for session if not exists
#         if session_id not in chat_messages:
#             chat_messages[session_id] = []

#         # Create chat message
#         chat_data = {
#             'id': str(uuid.uuid4()),
#             'session_id': session_id,
#             'sender_id': sender_id,
#             'sender_name': sender_name,
#             'sender_role': sender_role,
#             'message': message,
#             'timestamp': datetime.now(timezone.utc).isoformat()
#         }

#         # Store message
#         chat_messages[session_id].append(chat_data)

#         # Also emit via Socket.IO for real-time updates (if anyone is connected)
#         socketio.emit('chat_message', chat_data, room=session_id)

#         print(f"💬 HTTP Chat message from {sender_name} ({sender_role}) in session {session_id}: {message}")

#         return jsonify({
#             'success': True,
#             'message': 'Message sent successfully',
#             'chat_data': chat_data
#         }), 200

#     except Exception as e:
#         print(f"❌ Error sending chat message via HTTP: {e}")
#         return jsonify({'message': 'Failed to send message', 'error': str(e)}), 500

# @app.route('/api/chat/history/<session_id>', methods=['GET'])
# def get_chat_history(session_id):
#     """Get chat history for a session via HTTP"""
#     try:
#         if session_id and session_id in chat_messages:
#             history = chat_messages[session_id][-50:]  # Last 50 messages
#             return jsonify({
#                 'success': True,
#                 'session_id': session_id,
#                 'messages': history,
#                 'total_messages': len(history)
#             }), 200
#         else:
#             return jsonify({
#                 'success': True,
#                 'session_id': session_id,
#                 'messages': [],
#                 'total_messages': 0
#             }), 200

#     except Exception as e:
#         print(f"❌ Error getting chat history via HTTP: {e}")
#         return jsonify({'message': 'Failed to get chat history', 'error': str(e)}), 500

# @app.route('/api/enhanced-stream/start', methods=['POST'])
# def start_enhanced_stream():
#     """Start enhanced streaming session with LiveKit"""
#     try:
#         data = request.get_json()
#         session_id = data.get('session_id', str(uuid.uuid4()))
#         quality = data.get('quality', 'medium')
#         teacher_id = data.get('teacher_id', 'demo_teacher')
#         teacher_name = data.get('teacher_name', teacher_id)

#         stream = enhanced_stream_manager.create_enhanced_stream(
#             teacher_id, session_id, None, quality
#         )

#         teacher_token = livekit_manager.generate_access_token(
#             room_name=session_id,
#             participant_identity=teacher_id,
#             participant_name=teacher_name,
#             is_teacher=True
#         )

#         # Dynamically generate WebSocket URL
#         ws_scheme = 'wss' if request.scheme == 'https' else 'ws'
#         stream_url = f"{ws_scheme}://{request.host}/socket.io/"

#         return jsonify({
#             'message': 'Enhanced stream with LiveKit started successfully',
#             'session_id': session_id,
#             'stream_url': stream_url,
#             'livekit_url': LIVEKIT_URL,
#             'livekit_token': teacher_token,
#             'features': {
#                 'chat': True,
#                 'recording': True,
#                 'quality_controls': True,
#                 'screen_sharing': True,
#                 'livekit_enabled': True
#             },
#             'quality_settings': quality_settings[session_id]
#         }), 200
#     except Exception as e:
#         print(f"Enhanced stream start error: {e}")
#         return jsonify({'message': 'Failed to start enhanced stream'}), 500

# @app.route('/api/enhanced-stream/refresh-token', methods=['POST'])
# def refresh_livekit_token():
#     """Refresh LiveKit access token for long streaming sessions"""
#     try:
#         data = request.get_json()
#         session_id = data.get('session_id')
#         room_name = data.get('room_name', session_id)

#         if not session_id:
#             return jsonify({'message': 'session_id is required'}), 400

#         # Get the stream to verify it exists
#         stream = enhanced_stream_manager.get_stream(session_id)
#         if not stream:
#             return jsonify({'message': 'Stream session not found'}), 404

#         # Generate new token for the teacher
#         teacher_id = stream.get('teacher_id', 'demo_teacher')
#         teacher_name = stream.get('teacher_name', teacher_id)

#         new_token = livekit_manager.generate_access_token(
#             room_name=room_name,
#             participant_identity=teacher_id,
#             participant_name=teacher_name,
#             is_teacher=True
#         )

#         print(f"✅ Token refreshed for session {session_id}")

#         return jsonify({
#             'message': 'Token refreshed successfully',
#             'livekit_token': new_token,
#             'session_id': session_id,
#             'room_name': room_name
#         }), 200

#     except Exception as e:
#         print(f"❌ Token refresh error: {e}")
#         return jsonify({'message': 'Failed to refresh token', 'error': str(e)}), 500

# @app.route('/api/livekit/token', methods=['POST'])
# def generate_livekit_token():
#     """Generate LiveKit access token for participants"""
#     try:
#         data = request.get_json()
#         room_name = data.get('room_name') or data.get('session_id')
#         participant_id = data.get('participant_id') or data.get('user_id')
#         participant_name = data.get('participant_name', participant_id)
#         is_teacher = data.get('is_teacher', False)

#         if not room_name or not participant_id:
#             return jsonify({'message': 'room_name and participant_id are required'}), 400

#         stream = enhanced_stream_manager.get_stream(room_name)
#         if not stream:
#             return jsonify({'message': 'Stream not found'}), 404

#         token = livekit_manager.generate_access_token(
#             room_name=room_name,
#             participant_identity=participant_id,
#             participant_name=participant_name,
#             is_teacher=is_teacher
#         )

#         if token:
#             return jsonify({
#                 'token': token,
#                 'livekit_url': LIVEKIT_URL,
#                 'room_name': room_name,
#                 'participant_id': participant_id,
#                 'is_teacher': is_teacher
#             }), 200
#         else:
#             return jsonify({'message': 'Failed to generate token'}), 500
#     except Exception as e:
#         print(f"Token generation error: {e}")
#         return jsonify({'message': 'Failed to generate token'}), 500

# @app.route('/api/livekit/join', methods=['POST'])
# def join_livekit_room():
#     """Join LiveKit room and get connection details"""
#     try:
#         data = request.get_json()
#         session_id = data.get('session_id')
#         user_id = data.get('user_id')
#         user_name = data.get('user_name', user_id)
#         user_role = data.get('user_role', 'student')

#         if not session_id or not user_id:
#             return jsonify({'message': 'session_id and user_id are required'}), 400

#         stream = enhanced_stream_manager.get_stream(session_id)
#         if not stream:
#             return jsonify({'message': 'Stream not found'}), 404

#         is_teacher = (user_role in ['faculty', 'kota_teacher'] or user_id == stream.get('teacher_id'))
#         token = livekit_manager.generate_access_token(
#             room_name=session_id,
#             participant_identity=user_id,
#             participant_name=user_name,
#             is_teacher=is_teacher
#         )

#         if token:
#             return jsonify({
#                 'success': True,
#                 'livekit_url': LIVEKIT_URL,
#                 'token': token,
#                 'room_name': session_id,
#                 'participant_id': user_id,
#                 'participant_name': user_name,
#                 'is_teacher': is_teacher,
#                 'stream_info': {
#                     'session_id': session_id,
#                     'teacher_id': stream['teacher_id'],
#                     'viewer_count': stream['viewer_count'],
#                     'quality': stream['quality'],
#                     'features': {
#                         'chat_enabled': stream['chat_enabled'],
#                         'recording_enabled': stream['recording_enabled'],
#                         'screen_sharing': stream['screen_sharing']
#                     }
#                 }
#             }), 200
#         else:
#             return jsonify({'message': 'Failed to generate access token'}), 500
#     except Exception as e:
#         print(f"LiveKit join error: {e}")
#         return jsonify({'message': 'Failed to join LiveKit room'}), 500

# @app.route('/api/enhanced-stream/stop', methods=['POST'])
# def stop_enhanced_stream():
#     """Stop enhanced streaming session"""
#     try:
#         data = request.get_json() or {}
#         session_id = data.get('session_id')
#         teacher_id = data.get('teacher_id')

#         print(f"🛑 Received stop request for session: {session_id}, teacher: {teacher_id}")
#         if not session_id and teacher_id:
#             for sid, stream in enhanced_stream_manager.streams.items():
#                 if stream.get('teacher_id') == teacher_id and stream.get('status') == 'active':
#                     session_id = sid
#                     print(f"🔍 Found active stream for teacher {teacher_id}: {session_id}")
#                     break

#         if not session_id:
#             active_sessions = list(enhanced_stream_manager.streams.keys())
#             if active_sessions:
#                 print(f"⚠ No session_id provided, stopping all active streams: {active_sessions}")
#                 stopped_count = 0
#                 for sid in active_sessions:
#                     if enhanced_stream_manager.stop_stream(sid):
#                         socketio.emit('stream_ended', {
#                             'session_id': sid,
#                             'message': 'Stream has ended'
#                         }, room=sid)
#                         stopped_count += 1
#                 print(f"✅ Stopped {stopped_count} streams and notified all viewers")
#                 return jsonify({
#                     'message': f'Stopped {stopped_count} active streams',
#                     'stopped_sessions': active_sessions,
#                     'recording_saved': True
#                 }), 200
#             else:
#                 return jsonify({'message': 'No active streams found'}), 404

#         success = enhanced_stream_manager.stop_stream(session_id)
#         if success:
#             socketio.emit('stream_ended', {
#                 'session_id': session_id,
#                 'message': 'Stream has ended'
#             }, room=session_id)
#             print(f"✅ Successfully stopped stream {session_id} and notified viewers")
#             return jsonify({
#                 'message': 'Enhanced stream stopped successfully',
#                 'session_id': session_id,
#                 'recording_saved': True
#             }), 200
#         else:
#             print(f"❌ Stream {session_id} not found")
#             return jsonify({'message': 'Stream not found'}), 404
#     except Exception as e:
#         print(f"❌ Enhanced stream stop error: {e}")
#         return jsonify({'message': 'Failed to stop enhanced stream', 'error': str(e)}), 500

# @app.route('/api/enhanced-stream/status/<session_id>', methods=['GET'])
# def get_enhanced_stream_status(session_id):
#     """Get enhanced stream status"""
#     stream = enhanced_stream_manager.get_stream(session_id)
#     if stream:
#         return jsonify({
#             'session_id': session_id,
#             'status': stream['status'],
#             'viewer_count': stream['viewer_count'],
#             'quality': stream['quality'],
#             'features': {
#                 'chat_enabled': stream['chat_enabled'],
#                 'recording_enabled': stream['recording_enabled'],
#                 'screen_sharing': stream['screen_sharing']
#             },
#             'uptime': (datetime.now(timezone.utc) - stream['created_at']).total_seconds()
#         }), 200
#     else:
#         return jsonify({'message': 'Stream not found'}), 404

# @app.route('/active-streams', methods=['GET'])
# def get_active_streams():
#     """Get all active enhanced streams"""
#     try:
#         active_streams = []
#         for session_id, stream in enhanced_stream_manager.streams.items():
#             if stream['status'] == 'active':
#                 stream_info = {
#                     'session_id': session_id,
#                     'teacher_id': stream['teacher_id'],
#                     'viewer_count': stream['viewer_count'],
#                     'quality': stream['quality'],
#                     'created_at': stream['created_at'].isoformat(),
#                     'uptime': (datetime.now(timezone.utc) - stream['created_at']).total_seconds(),
#                     'features': {
#                         'chat_enabled': stream['chat_enabled'],
#                         'recording_enabled': stream['recording_enabled'],
#                         'screen_sharing': stream['screen_sharing']
#                     }
#                 }
#                 active_streams.append(stream_info)
#         return jsonify({
#             'success': True,
#             'streams': active_streams,
#             'active_streams': active_streams,
#             'total_count': len(active_streams),
#             'service': 'Enhanced Real-time Streaming',
#             'timestamp': datetime.now(timezone.utc).isoformat()
#         }), 200
#     except Exception as e:
#         print(f"❌ Error getting active streams: {e}")
#         return jsonify({'message': 'Failed to get active streams', 'error': str(e)}), 500

# @app.route('/api/enhanced-stream/list', methods=['GET'])
# def list_enhanced_streams():
#     """List all enhanced streams (active and inactive)"""
#     try:
#         all_streams = []
#         for session_id, stream in enhanced_stream_manager.streams.items():
#             stream_info = {
#                 'session_id': session_id,
#                 'teacher_id': stream['teacher_id'],
#                 'status': stream['status'],
#                 'viewer_count': stream['viewer_count'],
#                 'quality': stream['quality'],
#                 'created_at': stream['created_at'].isoformat(),
#                 'uptime': (datetime.now(timezone.utc) - stream['created_at']).total_seconds(),
#                 'features': {
#                     'chat_enabled': stream['chat_enabled'],
#                     'recording_enabled': stream['recording_enabled'],
#                     'screen_sharing': stream['screen_sharing']
#                 }
#             }
#             all_streams.append(stream_info)
#         for session_id, recording in enhanced_stream_manager.recordings.items():
#             recording_info = {
#                 'session_id': session_id,
#                 'teacher_id': recording['teacher_id'],
#                 'status': 'recorded',
#                 'duration': recording['duration'],
#                 'quality': recording['quality'],
#                 'viewer_count': recording['viewer_count'],
#                 'chat_messages': recording['chat_messages'],
#                 'recorded_at': recording['recorded_at'].isoformat()
#             }
#             all_streams.append(recording_info)
#         return jsonify({
#             'streams': all_streams,
#             'total_count': len(all_streams),
#             'active_count': len([s for s in all_streams if s['status'] == 'active']),
#             'recorded_count': len([s for s in all_streams if s['status'] == 'recorded']),
#             'service': 'Enhanced Real-time Streaming',
#             'timestamp': datetime.now(timezone.utc).isoformat()
#         }), 200
#     except Exception as e:
#         print(f"❌ Error listing streams: {e}")
#         return jsonify({'message': 'Failed to list streams', 'error': str(e)}), 500

# # Socket.IO Events
# @socketio.on('connect')
# def handle_connect():
#     print(f"✅ Enhanced client connected: {request.sid}")
#     emit('connected', {'message': 'Connected to enhanced streaming service'})

# @socketio.on('disconnect')
# def handle_disconnect():
#     print(f"❌ Enhanced client disconnected: {request.sid}")
#     for session_id in list(enhanced_stream_manager.streams.keys()):
#         enhanced_stream_manager.remove_viewer(session_id, request.sid)

# @socketio.on('start_stream')
# def handle_start_stream(data):
#     try:
#         session_id = data.get('session_id')
#         teacher_id = data.get('teacher_id')
#         teacher_name = data.get('teacher_name', teacher_id)
#         quality = data.get('quality', 'medium')

#         if not session_id or not teacher_id:
#             emit('error', {'message': 'Session ID and Teacher ID required'})
#             return

#         stream = enhanced_stream_manager.create_enhanced_stream(
#             teacher_id, session_id, request.sid, quality
#         )
#         join_room(session_id)
#         teacher_token = livekit_manager.generate_access_token(
#             room_name=session_id,
#             participant_identity=teacher_id,
#             participant_name=teacher_name,
#             is_teacher=True
#         )
#         emit('stream_started', {
#             'session_id': session_id,
#             'message': 'Stream started successfully with LiveKit',
#             'livekit_url': LIVEKIT_URL,
#             'livekit_token': teacher_token,
#             'features': {
#                 'chat': True,
#                 'recording': True,
#                 'quality_controls': True,
#                 'screen_sharing': True,
#                 'livekit_enabled': True
#             }
#         })
#         print(f"🎬 Teacher {teacher_id} started LiveKit stream {session_id}")
#     except Exception as e:
#         print(f"❌ Error starting stream: {e}")
#         emit('error', {'message': f'Failed to start stream: {str(e)}'})

# @socketio.on('stop_stream')
# def handle_stop_stream(data):
#     try:
#         session_id = data.get('session_id')
#         teacher_id = data.get('teacher_id')
#         print(f"🛑 Socket stop_stream request: session={session_id}, teacher={teacher_id}")

#         if not session_id and teacher_id:
#             for sid, stream in enhanced_stream_manager.streams.items():
#                 if stream.get('teacher_id') == teacher_id and stream.get('status') == 'active':
#                     session_id = sid
#                     print(f"🔍 Found active stream for teacher {teacher_id}: {session_id}")
#                     break

#         if not session_id:
#             emit('error', {'message': 'Session ID required or no active stream found'})
#             return

#         success = enhanced_stream_manager.stop_stream(session_id)
#         if success:
#             socketio.emit('stream_ended', {
#                 'session_id': session_id,
#                 'message': 'Stream has ended'
#             }, room=session_id)
#             leave_room(session_id)
#             emit('stream_stopped', {
#                 'session_id': session_id,
#                 'message': 'Stream stopped successfully'
#             })
#             print(f"✅ Teacher {teacher_id} stopped stream {session_id}")
#         else:
#             emit('error', {'message': 'Failed to stop stream - stream not found'})
#     except Exception as e:
#         print(f"❌ Error stopping stream: {e}")
#         emit('error', {'message': f'Failed to stop stream: {str(e)}'})

# @socketio.on('end_stream')
# def handle_end_stream(data):
#     try:
#         session_id = data.get('session_id')
#         teacher_id = data.get('teacher_id')
#         print(f"🔄 Received end_stream event for session {session_id}, teacher {teacher_id}")
#         print(f"🔄 Forwarding to stop_stream handler for backward compatibility")
#         handle_stop_stream(data)
#     except Exception as e:
#         print(f"❌ Error handling end_stream: {e}")
#         emit('error', {'message': f'Failed to end stream: {str(e)}'})

# @socketio.on('join_stream')
# def handle_join_stream(data):
#     try:
#         session_id = data.get('session_id')
#         faculty_id = data.get('faculty_id') or data.get('viewer_id') or data.get('user_id')
#         user_name = data.get('user_name') or data.get('viewer_name') or faculty_id
#         user_role = data.get('user_role', 'student')

#         if not session_id:
#             emit('error', {'message': 'Session ID required'})
#             return

#         stream = enhanced_stream_manager.get_stream(session_id)
#         if not stream:
#             emit('error', {'message': 'Stream not found'})
#             return

#         join_room(session_id)
#         viewer_data = {
#             'viewer_id': faculty_id,
#             'viewer_name': user_name,
#             'user_role': user_role,
#             'socket_id': request.sid,
#             'joined_at': datetime.now(timezone.utc)
#         }
#         if 'viewer_details' not in stream:
#             stream['viewer_details'] = {}
#         stream['viewer_details'][faculty_id] = viewer_data

#         success = enhanced_stream_manager.add_viewer(session_id, faculty_id, request.sid)
#         if success:
#             is_teacher = (user_role in ['faculty', 'kota_teacher'] or faculty_id == stream.get('teacher_id'))
#             viewer_token = livekit_manager.generate_access_token(
#                 room_name=session_id,
#                 participant_identity=faculty_id,
#                 participant_name=user_name,
#                 is_teacher=is_teacher
#             )
#             emit('stream_joined', {
#                 'session_id': session_id,
#                 'viewer_count': stream['viewer_count'],
#                 'message': 'Successfully joined stream',
#                 'livekit_url': LIVEKIT_URL,
#                 'livekit_token': viewer_token,
#                 'is_teacher': is_teacher,
#                 'stream_info': {
#                     'teacher_id': stream['teacher_id'],
#                     'quality': stream['quality'],
#                     'features': {
#                         'chat_enabled': stream['chat_enabled'],
#                         'recording_enabled': stream['recording_enabled'],
#                         'screen_sharing': stream['screen_sharing']
#                     }
#                 }
#             })
#             emit('viewer_joined', {
#                 'viewer_id': faculty_id,
#                 'viewer_name': user_name,
#                 'viewer_count': stream['viewer_count'],
#                 'is_teacher': is_teacher,
#                 'user_role': user_role
#             }, room=session_id, include_self=False)
#             print(f"👥 Viewer {faculty_id} joined LiveKit stream {session_id}")
#         else:
#             emit('error', {'message': 'Failed to join stream'})
#     except Exception as e:
#         print(f"❌ Error joining stream: {e}")
#         emit('error', {'message': f'Failed to join stream: {str(e)}'})

# @socketio.on('leave_stream')
# def handle_leave_stream(data):
#     try:
#         session_id = data.get('session_id')
#         faculty_id = data.get('faculty_id') or data.get('viewer_id')
#         if session_id:
#             leave_room(session_id)
#             enhanced_stream_manager.remove_viewer(session_id, request.sid)
#             stream = enhanced_stream_manager.get_stream(session_id)
#             if stream:
#                 if 'viewer_details' in stream and faculty_id in stream['viewer_details']:
#                     del stream['viewer_details'][faculty_id]
#                 emit('viewer_left', {
#                     'viewer_id': faculty_id,
#                     'viewer_count': stream['viewer_count']
#                 }, room=session_id)
#                 print(f"👋 Viewer {faculty_id} left stream {session_id}")
#     except Exception as e:
#         print(f"❌ Error leaving stream: {e}")

# @socketio.on('join_enhanced_stream')
# def handle_join_enhanced_stream(data):
#     try:
#         session_id = data.get('session_id')
#         viewer_id = data.get('viewer_id')
#         viewer_name = data.get('viewer_name', viewer_id)
#         user_role = data.get('user_role', 'student')

#         if not session_id:
#             emit('error', {'message': 'Session ID required'})
#             return

#         join_room(session_id)
#         success = enhanced_stream_manager.add_viewer(session_id, viewer_id, request.sid)
#         if success:
#             stream = enhanced_stream_manager.get_stream(session_id)
#             is_teacher = (user_role in ['faculty', 'kota_teacher'] or viewer_id == stream.get('teacher_id'))
#             viewer_token = livekit_manager.generate_access_token(
#                 room_name=session_id,
#                 participant_identity=viewer_id,
#                 participant_name=viewer_name,
#                 is_teacher=is_teacher
#             )
#             emit('joined_enhanced_stream', {
#                 'session_id': session_id,
#                 'viewer_count': stream['viewer_count'],
#                 'quality_settings': quality_settings.get(session_id, {}),
#                 'chat_history': chat_messages.get(session_id, [])[-50:],
#                 'livekit_url': LIVEKIT_URL,
#                 'livekit_token': viewer_token,
#                 'is_teacher': is_teacher
#             })
#             emit('viewer_joined', {
#                 'viewer_id': viewer_id,
#                 'viewer_name': viewer_name,
#                 'viewer_count': stream['viewer_count'],
#                 'is_teacher': is_teacher
#             }, room=session_id, include_self=False)
#         else:
#             emit('error', {'message': 'Failed to join stream'})
#     except Exception as e:
#         print(f"❌ Error joining enhanced stream: {e}")
#         emit('error', {'message': 'Failed to join stream'})

# @socketio.on('request_livekit_token')
# def handle_request_livekit_token(data):
#     try:
#         session_id = data.get('session_id')
#         user_id = data.get('user_id')
#         user_name = data.get('user_name', user_id)
#         user_role = data.get('user_role', 'student')

#         if not session_id or not user_id:
#             emit('error', {'message': 'Session ID and User ID required'})
#             return

#         stream = enhanced_stream_manager.get_stream(session_id)
#         if not stream:
#             emit('error', {'message': 'Stream not found'})
#             return

#         is_teacher = (user_role in ['faculty', 'kota_teacher'] or user_id == stream.get('teacher_id'))
#         token = livekit_manager.generate_access_token(
#             room_name=session_id,
#             participant_identity=user_id,
#             participant_name=user_name,
#             is_teacher=is_teacher
#         )
#         if token:
#             emit('livekit_token_generated', {
#                 'session_id': session_id,
#                 'livekit_url': LIVEKIT_URL,
#                 'livekit_token': token,
#                 'participant_id': user_id,
#                 'participant_name': user_name,
#                 'is_teacher': is_teacher
#             })
#         else:
#             emit('error', {'message': 'Failed to generate LiveKit token'})
#     except Exception as e:
#         print(f"❌ Error generating LiveKit token: {e}")
#         emit('error', {'message': 'Failed to generate token'})

# @socketio.on('leave_enhanced_stream')
# def handle_leave_enhanced_stream(data):
#     try:
#         session_id = data.get('session_id')
#         if session_id:
#             leave_room(session_id)
#             enhanced_stream_manager.remove_viewer(session_id, request.sid)
#             stream = enhanced_stream_manager.get_stream(session_id)
#             if stream:
#                 emit('viewer_left', {
#                     'viewer_count': stream['viewer_count']
#                 }, room=session_id)
#     except Exception as e:
#         print(f"❌ Error leaving enhanced stream: {e}")

# @socketio.on('enhanced_video_frame')
# def handle_enhanced_video_frame(data):
#     try:
#         session_id = data.get('session_id')
#         frame_data = data.get('frame_data')
#         quality = data.get('quality', 'medium')
#         if not session_id or not frame_data:
#             return
#         if session_id in quality_settings:
#             quality_settings[session_id]['video_quality'] = quality
#         socketio.emit('enhanced_video_frame', {
#             'session_id': session_id,
#             'frame_data': frame_data,
#             'quality': quality,
#             'timestamp': datetime.now(timezone.utc).isoformat()
#         }, room=session_id, include_self=False)
#     except Exception as e:
#         print(f"❌ Error handling enhanced video frame: {e}")

# @socketio.on('chat_message')
# def handle_chat_message(data):
#     try:
#         session_id = data.get('session_id')
#         message = data.get('message')
#         sender_id = data.get('sender_id')
#         sender_name = data.get('sender_name', 'Anonymous')
#         if not session_id or not message:
#             return
#         sender_role = get_user_role_by_id(sender_id) or 'unknown'
#         if session_id not in chat_messages:
#             chat_messages[session_id] = []
#         chat_data = {
#             'id': str(uuid.uuid4()),
#             'session_id': session_id,
#             'sender_id': sender_id,
#             'sender_name': sender_name,
#             'sender_role': sender_role,
#             'message': message,
#             'timestamp': datetime.now(timezone.utc).isoformat()
#         }
#         chat_messages[session_id].append(chat_data)
#         socketio.emit('chat_message', chat_data, room=session_id)
#         print(f"💬 Chat message from {sender_name} ({sender_role}) in session {session_id}: {message}")
#     except Exception as e:
#         print(f"❌ Error handling chat message: {e}")

# @socketio.on('get_chat_history')
# def handle_get_chat_history(data):
#     try:
#         session_id = data.get('session_id')
#         if session_id and session_id in chat_messages:
#             history = chat_messages[session_id][-50:]
#             emit('chat_history', {
#                 'session_id': session_id,
#                 'messages': history
#             })
#         else:
#             emit('chat_history', {
#                 'session_id': session_id,
#                 'messages': []
#             })
#     except Exception as e:
#         print(f"❌ Error getting chat history: {e}")

# @socketio.on('video_frame')
# def handle_video_frame_legacy(data):
#     try:
#         session_id = data.get('session_id')
#         frame_data = data.get('frame_data')
#         frame_type = data.get('frame_type', 'camera')
#         if not session_id or not frame_data:
#             return
#         socketio.emit('video_frame', {
#             'session_id': session_id,
#             'frame_data': frame_data,
#             'frame_type': frame_type,
#             'timestamp': datetime.now(timezone.utc).isoformat()
#         }, room=session_id, include_self=False)
#     except Exception as e:
#         print(f"❌ Error handling video frame: {e}")

# @socketio.on('screen_frame')
# def handle_screen_frame_legacy(data):
#     try:
#         session_id = data.get('session_id')
#         frame_data = data.get('frame_data')
#         if not session_id or not frame_data:
#             return
#         socketio.emit('screen_frame', {
#             'session_id': session_id,
#             'frame_data': frame_data,
#             'timestamp': datetime.now(timezone.utc).isoformat()
#         }, room=session_id, include_self=False)
#     except Exception as e:
#         print(f"❌ Error handling screen frame: {e}")

# if __name__ == '__main__':
#     print("🚀 Starting Enhanced Real-time Streaming Service with LiveKit...")
#     print("🎯 Features: LiveKit + WebRTC + Socket.IO + Chat + Recording + Quality Controls")
#     print("🌐 CORS enabled for all origins with full preflight support")
#     print("🔧 Socket.IO CORS configured with credentials support")
#     print(f"🎥 LiveKit URL: {LIVEKIT_URL}")
#     print(f"🔑 LiveKit API Key: {LIVEKIT_API_KEY}")
#     print("🚀 Server starting on port 8012...")
#     print("=" * 70)

#     if not all([LIVEKIT_URL, LIVEKIT_API_KEY, LIVEKIT_API_SECRET]):
#         print("❌ LiveKit configuration missing! Please check your .env file.")
#         print("Required: LIVEKIT_URL, LIVEKIT_API_KEY, LIVEKIT_API_SECRET")
#         exit(1)

#     print("✅ LiveKit configuration validated")
#     print("🎬 Ready to create streaming rooms with LiveKit integration")
#     DEBUG = os.getenv('FLASK_DEBUG', 'False').lower() in ['true', '1', 't']

#     # For production deployment, use allow_unsafe_werkzeug=True
#     # Note: For better production performance, consider using Gunicorn
#     socketio.run(app, host='0.0.0.0', port=8012, debug=DEBUG, allow_unsafe_werkzeug=True)


from flask import Flask, request, jsonify
from flask_socketio import SocketIO, emit, join_room, leave_room
from flask_cors import CORS
import sys
import os
import json
import uuid
from datetime import datetime, timezone, timedelta
import base64
import asyncio
import threading
import subprocess
import tempfile
import queue
import requests
import jwt
import time

# LiveKit imports
from livekit import api, rtc
from livekit.api import AccessToken, VideoGrants, LiveKitAPI
from livekit.rtc import Room, TrackSource
from livekit.protocol import ingress as ingress_proto

# Add parent directory to path to import shared modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from dotenv import load_dotenv
from shared.database import Database  # Import Database

# Import ProxyFix for handling reverse proxy headers
from werkzeug.middleware.proxy_fix import ProxyFix

load_dotenv()

app = Flask(__name__)
app.config['SECRET_KEY'] = 'enhanced-streaming-secret-key'

# Configure CORS to allow all origins for development
CORS(app,
     origins="*",
     allow_headers=["Content-Type", "Authorization", "Access-Control-Allow-Credentials"],
     supports_credentials=True,
     methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"])

# Apply ProxyFix middleware to handle Nginx proxy headers
app.wsgi_app = ProxyFix(app.wsgi_app, x_for=1, x_proto=1, x_host=1, x_port=1, x_prefix=1)

# Initialize Socket.IO with CORS support for all origins
# async_mode will be auto-detected (eventlet in production, threading in development)
socketio = SocketIO(app,
                   cors_allowed_origins="*",
                   cors_credentials=True,
                   logger=True,
                   engineio_logger=True)

# Initialize Database
db = Database()

# Add CORS preflight handler
@app.before_request
def handle_preflight():
    if request.method == "OPTIONS":
        response = jsonify({'status': 'OK'})
        response.headers.add("Access-Control-Allow-Origin", "*")
        response.headers.add('Access-Control-Allow-Headers', "*")
        response.headers.add('Access-Control-Allow-Methods', "*")
        return response

# LiveKit Configuration from environment variables
LIVEKIT_URL = os.getenv('LIVEKIT_URL')
LIVEKIT_API_KEY = os.getenv('LIVEKIT_API_KEY')
LIVEKIT_API_SECRET = os.getenv('LIVEKIT_API_SECRET')

# Global variables for LiveKit async operations
livekit_api = None
livekit_loop = None
livekit_thread = None

def init_livekit_async():
    """Initialize LiveKit with a dedicated event loop in a background thread"""
    global livekit_api, livekit_loop, livekit_thread

    def run_livekit_loop():
        global livekit_api, livekit_loop
        livekit_loop = asyncio.new_event_loop()
        asyncio.set_event_loop(livekit_loop)

        try:
            # Create LiveKit API client in the async context
            livekit_api = LiveKitAPI(LIVEKIT_URL, LIVEKIT_API_KEY, LIVEKIT_API_SECRET)
            print("✅ LiveKit API client initialized successfully")

            # Keep the loop running
            livekit_loop.run_forever()
        except Exception as e:
            print(f"❌ Failed to initialize LiveKit: {e}")
        finally:
            livekit_loop.close()

    # Start the LiveKit thread
    livekit_thread = threading.Thread(target=run_livekit_loop, daemon=True)
    livekit_thread.start()

    # Wait a moment for initialization
    import time
    time.sleep(1)

def get_livekit_api():
    """Get the LiveKit API client"""
    global livekit_api
    if livekit_api is None:
        raise Exception("LiveKit API not initialized. Call init_livekit_async() first.")
    return livekit_api

def run_async_in_livekit_loop(coro):
    """Run async function in the LiveKit event loop"""
    global livekit_loop
    if livekit_loop is None or livekit_loop.is_closed():
        raise Exception("LiveKit event loop not available")

    future = asyncio.run_coroutine_threadsafe(coro, livekit_loop)
    return future.result(timeout=30)

# Global storage for enhanced streaming features
enhanced_streams = {}
stream_recordings = {}
chat_messages = {}
quality_settings = {}
livekit_rooms = {}  # Track LiveKit rooms
rtmp_ingress_sessions = {}  # Track RTMP ingress sessions

class LiveKitManager:
    """LiveKit integration manager for room and token management"""

    def __init__(self):
        self.rooms = {}

    def generate_access_token(self, room_name, participant_identity, participant_name=None, is_teacher=False):
        """Generate LiveKit access token for a participant"""
        try:
            token = AccessToken(LIVEKIT_API_KEY, LIVEKIT_API_SECRET)
            token.with_identity(participant_identity)
            token.with_name(participant_name or participant_identity)

            grants = VideoGrants(
                room_join=True,
                room=room_name,
                can_publish=is_teacher,
                can_subscribe=True,
                can_publish_data=True,
                can_update_own_metadata=True
            )

            if is_teacher:
                grants.room_admin = True

            token.with_grants(grants)
            token.with_ttl(timedelta(hours=24))
            jwt_token = token.to_jwt()
            print(f"✅ Generated LiveKit token for {participant_identity} in room {room_name}")
            return jwt_token
        except Exception as e:
            print(f"❌ Error generating LiveKit token: {e}")
            return None

    async def create_room(self, room_name, max_participants=50):
        """Create a LiveKit room"""
        try:
            room_request = api.CreateRoomRequest(
                name=room_name,
                max_participants=max_participants,
                empty_timeout=10 * 60,
                departure_timeout=60
            )
            api_client = get_livekit_api()
            room = await api_client.room.create_room(room_request)
            self.rooms[room_name] = {
                'room': room,
                'created_at': datetime.now(timezone.utc),
                'participants': [],
                'max_participants': max_participants
            }
            print(f"✅ Created LiveKit room: {room_name}")
            return room
        except Exception as e:
            print(f"❌ Error creating LiveKit room {room_name}: {e}")
            return None

    async def delete_room(self, room_name):
        """Delete a LiveKit room"""
        try:
            api_client = get_livekit_api()
            await api_client.room.delete_room(api.DeleteRoomRequest(room=room_name))
            if room_name in self.rooms:
                del self.rooms[room_name]
            print(f"✅ Deleted LiveKit room: {room_name}")
            return True
        except Exception as e:
            print(f"❌ Error deleting LiveKit room {room_name}: {e}")
            return False

    async def list_participants(self, room_name):
        """List participants in a LiveKit room"""
        try:
            api_client = get_livekit_api()
            response = await api_client.room.list_participants(api.ListParticipantsRequest(room=room_name))
            return response.participants
        except Exception as e:
            print(f"❌ Error listing participants in room {room_name}: {e}")
            return []

    async def remove_participant(self, room_name, participant_identity):
        """Remove a participant from a LiveKit room"""
        try:
            api_client = get_livekit_api()
            await api_client.room.remove_participant(api.RoomParticipantIdentity(
                room=room_name,
                identity=participant_identity
            ))
            print(f"✅ Removed participant {participant_identity} from room {room_name}")
            return True
        except Exception as e:
            print(f"❌ Error removing participant {participant_identity} from room {room_name}: {e}")
            return False

# Initialize LiveKit Manager
livekit_manager = LiveKitManager()

class RTMPIngressManager:
    """Manage RTMP ingress sessions for streaming"""

    def __init__(self):
        self.ingress_sessions = {}

    def create_rtmp_ingress_rest(self, session_id, teacher_id, teacher_name):
        """Create RTMP ingress using REST API instead of async SDK"""
        try:
            print(f"🔄 RTMPIngressManager.create_rtmp_ingress_rest called for session: {session_id}")

            # Validate inputs
            if not all([session_id, teacher_id, teacher_name]):
                print(f"❌ Missing required parameters: session_id={session_id}, teacher_id={teacher_id}, teacher_name={teacher_name}")
                return None

            # Validate environment variables
            if not all([LIVEKIT_API_KEY, LIVEKIT_API_SECRET, LIVEKIT_URL]):
                print(f"❌ Missing LiveKit configuration")
                return None

            print(f"🔄 Creating JWT token...")
            # Create JWT token for authentication
            payload = {
                'iss': LIVEKIT_API_KEY,
                'exp': int(time.time()) + 3600,  # 1 hour expiry
                'nbf': int(time.time()) - 10,    # Not before (10 seconds ago)
                'video': {
                    'ingressAdmin': True
                }
            }

            token = jwt.encode(payload, LIVEKIT_API_SECRET, algorithm='HS256')
            print(f"✅ JWT token created successfully")

            # Prepare the request
            url = f"{LIVEKIT_URL.replace('wss://', 'https://').replace('ws://', 'http://')}/twirp/livekit.Ingress/CreateIngress"
            print(f"🔄 Making request to: {url}")

            headers = {
                'Authorization': f'Bearer {token}',
                'Content-Type': 'application/json'
            }

            data = {
                'input_type': 'RTMP_INPUT',
                'name': f'Stream-{session_id}',
                'room_name': session_id,
                'participant_identity': teacher_id,
                'participant_name': teacher_name,
                'enable_transcoding': True,
                'video': {
                    'source': 'CAMERA',
                    'preset': 'H264_1080P_30FPS_3_LAYERS'
                },
                'audio': {
                    'source': 'MICROPHONE',
                    'preset': 'OPUS_STEREO_96KBPS'
                }
            }

            print(f"🔄 Sending request with data: {data}")
            response = requests.post(url, json=data, headers=headers, timeout=30)
            print(f"🔄 Response status: {response.status_code}")

            if response.status_code == 200:
                ingress_info = response.json()

                # Store ingress information
                self.ingress_sessions[session_id] = {
                    'ingress_id': ingress_info.get('ingress_id'),
                    'rtmp_url': ingress_info.get('url'),
                    'stream_key': ingress_info.get('stream_key'),
                    'teacher_id': teacher_id,
                    'teacher_name': teacher_name,
                    'created_at': datetime.now(timezone.utc),
                    'status': 'created'
                }

                # Also store in global dict for easy access
                rtmp_ingress_sessions[session_id] = self.ingress_sessions[session_id]

                print(f"✅ Created RTMP ingress for session {session_id}: {ingress_info.get('url')}")

                # Return a simple object with the required attributes
                return IngressInfo(ingress_info)
            else:
                print(f"❌ Failed to create RTMP ingress: {response.status_code} - {response.text}")
                return None

        except RecursionError as e:
            print(f"❌ Recursion error in create_rtmp_ingress_rest for session {session_id}: {e}")
            import traceback
            print(f"❌ Recursion traceback: {traceback.format_exc()}")
            return None
        except Exception as e:
            print(f"❌ Failed to create RTMP ingress for session {session_id}: {e}")
            print(f"❌ Exception type: {type(e)}")
            import traceback
            print(f"❌ Full traceback: {traceback.format_exc()}")
            return None

    async def create_rtmp_ingress(self, session_id, teacher_id, teacher_name):
        """Create RTMP ingress for a streaming session - wrapper for REST API"""
        return self.create_rtmp_ingress_rest(session_id, teacher_id, teacher_name)

    def delete_rtmp_ingress_rest(self, session_id):
        """Delete RTMP ingress using REST API"""
        try:
            if session_id not in self.ingress_sessions:
                print(f"⚠️ No ingress found for session {session_id}")
                return False

            ingress_data = self.ingress_sessions[session_id]

            # Create JWT token for authentication
            payload = {
                'iss': LIVEKIT_API_KEY,
                'exp': int(time.time()) + 3600,
                'nbf': int(time.time()) - 10,
                'video': {
                    'ingressAdmin': True
                }
            }

            token = jwt.encode(payload, LIVEKIT_API_SECRET, algorithm='HS256')

            # Prepare the request
            url = f"{LIVEKIT_URL.replace('wss://', 'https://').replace('ws://', 'http://')}/twirp/livekit.Ingress/DeleteIngress"

            headers = {
                'Authorization': f'Bearer {token}',
                'Content-Type': 'application/json'
            }

            data = {
                'ingress_id': ingress_data['ingress_id']
            }

            response = requests.post(url, json=data, headers=headers, timeout=30)

            if response.status_code == 200:
                # Remove from storage
                del self.ingress_sessions[session_id]
                if session_id in rtmp_ingress_sessions:
                    del rtmp_ingress_sessions[session_id]

                print(f"✅ Deleted RTMP ingress for session {session_id}")
                return True
            else:
                print(f"❌ Failed to delete RTMP ingress: {response.status_code} - {response.text}")
                return False

        except Exception as e:
            print(f"❌ Failed to delete RTMP ingress for session {session_id}: {e}")
            return False

    async def delete_rtmp_ingress(self, session_id):
        """Delete RTMP ingress for a streaming session - wrapper for REST API"""
        return self.delete_rtmp_ingress_rest(session_id)

    async def list_rtmp_ingresses(self):
        """List all RTMP ingresses"""
        try:
            lkapi = get_livekit_api()
            response = await lkapi.ingress.list_ingress(api.ListIngressRequest())
            return response.items
        except Exception as e:
            print(f"❌ Failed to list RTMP ingresses: {e}")
            return []

    def get_ingress_info(self, session_id):
        """Get ingress information for a session"""
        return self.ingress_sessions.get(session_id)

# Simple class to hold ingress information
class IngressInfo:
    def __init__(self, data):
        self.ingress_id = data.get('ingress_id')
        self.url = data.get('url')
        self.stream_key = data.get('stream_key')

# Initialize RTMP Ingress Manager
rtmp_ingress_manager = RTMPIngressManager()

class RTMPRelayManager:
    """Manage RTMP relay sessions for browser-to-RTMP streaming"""

    def __init__(self):
        self.relay_sessions = {}
        self.ffmpeg_processes = {}
        self.media_queues = {}

    def start_rtmp_relay(self, session_id, rtmp_url, stream_key):
        """Start RTMP relay for a session"""
        try:
            full_rtmp_url = f"{rtmp_url}/{stream_key}"

            # Create media queue for this session
            self.media_queues[session_id] = queue.Queue()

            # Start FFmpeg process for RTMP streaming
            ffmpeg_cmd = [
                'ffmpeg',
                '-f', 'webm',
                '-i', 'pipe:0',  # Read from stdin
                '-c:v', 'libx264',
                '-preset', 'ultrafast',
                '-tune', 'zerolatency',
                '-g', '30',
                '-keyint_min', '30',
                '-sc_threshold', '0',
                '-c:a', 'aac',
                '-ar', '44100',
                '-b:a', '128k',
                '-f', 'flv',
                full_rtmp_url
            ]

            # Start FFmpeg process
            process = subprocess.Popen(
                ffmpeg_cmd,
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                bufsize=0
            )

            self.ffmpeg_processes[session_id] = process
            self.relay_sessions[session_id] = {
                'rtmp_url': rtmp_url,
                'stream_key': stream_key,
                'full_url': full_rtmp_url,
                'started_at': datetime.now(timezone.utc),
                'status': 'active'
            }

            # Start media processing thread
            threading.Thread(
                target=self._process_media_queue,
                args=(session_id,),
                daemon=True
            ).start()

            print(f"✅ Started RTMP relay for session {session_id} to {full_rtmp_url}")
            return True

        except Exception as e:
            print(f"❌ Failed to start RTMP relay for session {session_id}: {e}")
            return False

    def add_media_data(self, session_id, media_data):
        """Add media data to the relay queue"""
        if session_id in self.media_queues:
            try:
                self.media_queues[session_id].put(media_data, timeout=1)
                return True
            except queue.Full:
                print(f"⚠️ Media queue full for session {session_id}")
                return False
        return False

    def _process_media_queue(self, session_id):
        """Process media data from queue and send to FFmpeg"""
        try:
            process = self.ffmpeg_processes.get(session_id)
            media_queue = self.media_queues.get(session_id)

            if not process or not media_queue:
                return

            while session_id in self.relay_sessions:
                try:
                    # Get media data from queue (blocking with timeout)
                    media_data = media_queue.get(timeout=5)

                    if media_data and process.stdin:
                        process.stdin.write(media_data)
                        process.stdin.flush()

                except queue.Empty:
                    # Check if process is still alive
                    if process.poll() is not None:
                        print(f"⚠️ FFmpeg process died for session {session_id}")
                        break
                    continue
                except Exception as e:
                    print(f"❌ Error processing media for session {session_id}: {e}")
                    break

        except Exception as e:
            print(f"❌ Media processing thread error for session {session_id}: {e}")
        finally:
            self.stop_rtmp_relay(session_id)

    def stop_rtmp_relay(self, session_id):
        """Stop RTMP relay for a session"""
        try:
            # Stop FFmpeg process
            if session_id in self.ffmpeg_processes:
                process = self.ffmpeg_processes[session_id]
                if process.stdin:
                    process.stdin.close()
                process.terminate()

                # Wait for process to terminate
                try:
                    process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    process.kill()

                del self.ffmpeg_processes[session_id]

            # Clear media queue
            if session_id in self.media_queues:
                del self.media_queues[session_id]

            # Remove session
            if session_id in self.relay_sessions:
                del self.relay_sessions[session_id]

            print(f"✅ Stopped RTMP relay for session {session_id}")
            return True

        except Exception as e:
            print(f"❌ Failed to stop RTMP relay for session {session_id}: {e}")
            return False

    def get_relay_status(self, session_id):
        """Get relay status for a session"""
        return self.relay_sessions.get(session_id)

    def list_active_relays(self):
        """List all active relay sessions"""
        return list(self.relay_sessions.keys())

# Initialize RTMP Relay Manager
rtmp_relay_manager = RTMPRelayManager()

class EnhancedStreamManager:
    def __init__(self):
        self.streams = {}
        self.connections = {}
        self.recordings = {}

    def create_enhanced_stream(self, teacher_id, session_id, socket_id, quality='medium'):
        """Create an enhanced streaming session with LiveKit integration"""
        stream_data = {
            'teacher_id': teacher_id,
            'session_id': session_id,
            'teacher_socket': socket_id,
            'viewers': [],
            'created_at': datetime.now(timezone.utc),
            'status': 'active',
            'quality': quality,
            'recording_enabled': True,
            'chat_enabled': True,
            'screen_sharing': True,
            'viewer_count': 0,
            'livekit_room': session_id,
            'livekit_enabled': True
        }
        self.streams[session_id] = stream_data
        chat_messages[session_id] = []
        quality_settings[session_id] = {
            'video_quality': quality,
            'frame_rate': 30 if quality == 'high' else 20,
            'resolution': '1920x1080' if quality == 'high' else '1280x720'
        }

        def create_livekit_room():
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                loop.run_until_complete(livekit_manager.create_room(session_id))
            except Exception as e:
                print(f"❌ Error creating LiveKit room: {e}")
            finally:
                loop.close()

        threading.Thread(target=create_livekit_room, daemon=True).start()
        print(f"✅ Enhanced stream with LiveKit created: {session_id} by teacher {teacher_id}")
        return stream_data

    def add_viewer(self, session_id, viewer_id, socket_id):
        """Add a viewer to the enhanced stream"""
        if session_id in self.streams:
            viewer_data = {
                'viewer_id': viewer_id,
                'socket_id': socket_id,
                'joined_at': datetime.now(timezone.utc)
            }
            self.streams[session_id]['viewers'].append(viewer_data)
            self.streams[session_id]['viewer_count'] = len(self.streams[session_id]['viewers'])
            return True
        return False

    def remove_viewer(self, session_id, socket_id):
        """Remove a viewer from the enhanced stream"""
        if session_id in self.streams:
            self.streams[session_id]['viewers'] = [
                v for v in self.streams[session_id]['viewers']
                if v['socket_id'] != socket_id
            ]
            self.streams[session_id]['viewer_count'] = len(self.streams[session_id]['viewers'])
            return True
        return False

    def get_stream(self, session_id):
        """Get enhanced stream information"""
        return self.streams.get(session_id)

    def stop_stream(self, session_id):
        """Stop enhanced stream and save recording"""
        print(f"🛑 Attempting to stop stream: {session_id}")
        if session_id in self.streams:
            stream = self.streams[session_id]
            print(f"📊 Stream found - Teacher: {stream.get('teacher_id')}, Status: {stream.get('status')}, Viewers: {stream.get('viewer_count', 0)}")
            stream['status'] = 'stopped'
            stream['ended_at'] = datetime.now(timezone.utc)

            if stream.get('recording_enabled'):
                duration = (datetime.now(timezone.utc) - stream['created_at']).total_seconds()
                self.recordings[session_id] = {
                    'session_id': session_id,
                    'teacher_id': stream['teacher_id'],
                    'duration': duration,
                    'quality': stream['quality'],
                    'viewer_count': stream['viewer_count'],
                    'chat_messages': len(chat_messages.get(session_id, [])),
                    'recorded_at': datetime.now(timezone.utc)
                }
                print(f"💾 Recording saved - Duration: {duration:.1f}s, Quality: {stream['quality']}")

            if stream.get('livekit_enabled'):
                def delete_livekit_room():
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    try:
                        loop.run_until_complete(livekit_manager.delete_room(session_id))
                    except Exception as e:
                        print(f"❌ Error deleting LiveKit room: {e}")
                    finally:
                        loop.close()
                threading.Thread(target=delete_livekit_room, daemon=True).start()

            if session_id in chat_messages:
                del chat_messages[session_id]
                print(f"🧹 Cleaned up chat messages for session {session_id}")
            if session_id in quality_settings:
                del quality_settings[session_id]
                print(f"🧹 Cleaned up quality settings for session {session_id}")

            del self.streams[session_id]
            print(f"✅ Stream {session_id} stopped and cleaned up successfully")
            return True
        else:
            print(f"❌ Stream {session_id} not found in active streams")
            print(f"📋 Active streams: {list(self.streams.keys())}")
            return False

enhanced_stream_manager = EnhancedStreamManager()

def get_user_role_by_id(user_id):
    """Helper function to get user role by ID from various tables."""
    user = db.execute_query_one("SELECT role FROM users WHERE id = %s", (user_id,))
    if user:
        return user['role']
    center = db.execute_query_one("SELECT 'center_counselor' as role FROM centers WHERE center_code = %s", (user_id,))
    if center:
        return center['role']
    student = db.execute_query_one("SELECT 'student' as role FROM students WHERE id = %s", (user_id,))
    if student:
        return student['role']
    parent = db.execute_query_one("SELECT 'parent' as role FROM parents WHERE id = %s", (user_id,))
    if parent:
        return parent['role']
    faculty = db.execute_query_one("SELECT 'faculty' as role FROM faculty WHERE id = %s", (user_id,))
    if faculty:
        return faculty['role']
    teacher = db.execute_query_one("SELECT 'kota_teacher' as role FROM kota_teachers WHERE id = %s", (user_id,))
    if teacher:
        return teacher['role']
    return None

@app.route('/health', methods=['GET'])
def health_check():
    """Enhanced streaming service health check with LiveKit status"""
    livekit_status = 'configured' if all([LIVEKIT_URL, LIVEKIT_API_KEY, LIVEKIT_API_SECRET]) else 'not configured'
    return jsonify({
        'status': 'healthy',
        'service': 'Enhanced Real-time Streaming Service with LiveKit',
        'port': 8012,  # Updated to match the running port
        'features': ['LiveKit', 'WebRTC', 'Socket.IO', 'Chat', 'Recording', 'Quality Controls'],
        'active_streams': len(enhanced_stream_manager.streams),
        'livekit': {
            'status': livekit_status,
            'url': LIVEKIT_URL,
            'api_key': LIVEKIT_API_KEY[:8] + '...' if LIVEKIT_API_KEY else None,
            'rooms_managed': len(livekit_manager.rooms)
        },
        'timestamp': datetime.now().isoformat()
    }), 200

# HTTP-based Chat API Endpoints
@app.route('/api/chat/send', methods=['POST'])
def send_chat_message():
    """Send a chat message via HTTP"""
    try:
        data = request.get_json()
        session_id = data.get('session_id')
        message = data.get('message')
        sender_id = data.get('sender_id')
        sender_name = data.get('sender_name', 'Anonymous')

        if not session_id or not message:
            return jsonify({'message': 'session_id and message are required'}), 400

        # Get sender role
        sender_role = get_user_role_by_id(sender_id) or 'unknown'

        # Initialize chat messages for session if not exists
        if session_id not in chat_messages:
            chat_messages[session_id] = []

        # Create chat message
        chat_data = {
            'id': str(uuid.uuid4()),
            'session_id': session_id,
            'sender_id': sender_id,
            'sender_name': sender_name,
            'sender_role': sender_role,
            'message': message,
            'timestamp': datetime.now(timezone.utc).isoformat()
        }

        # Store message
        chat_messages[session_id].append(chat_data)

        # Also emit via Socket.IO for real-time updates (if anyone is connected)
        socketio.emit('chat_message', chat_data, room=session_id)

        print(f"💬 HTTP Chat message from {sender_name} ({sender_role}) in session {session_id}: {message}")

        return jsonify({
            'success': True,
            'message': 'Message sent successfully',
            'chat_data': chat_data
        }), 200

    except Exception as e:
        print(f"❌ Error sending chat message via HTTP: {e}")
        return jsonify({'message': 'Failed to send message', 'error': str(e)}), 500

@app.route('/api/chat/history/<session_id>', methods=['GET'])
def get_chat_history(session_id):
    """Get chat history for a session via HTTP"""
    try:
        if session_id and session_id in chat_messages:
            history = chat_messages[session_id][-50:]  # Last 50 messages
            return jsonify({
                'success': True,
                'session_id': session_id,
                'messages': history,
                'total_messages': len(history)
            }), 200
        else:
            return jsonify({
                'success': True,
                'session_id': session_id,
                'messages': [],
                'total_messages': 0
            }), 200

    except Exception as e:
        print(f"❌ Error getting chat history via HTTP: {e}")
        return jsonify({'message': 'Failed to get chat history', 'error': str(e)}), 500

@app.route('/api/enhanced-stream/start', methods=['POST'])
def start_enhanced_stream():
    """Start enhanced streaming session with LiveKit"""
    try:
        data = request.get_json()
        session_id = data.get('session_id', str(uuid.uuid4()))
        quality = data.get('quality', 'medium')
        teacher_id = data.get('teacher_id', 'demo_teacher')
        teacher_name = data.get('teacher_name', teacher_id)

        stream = enhanced_stream_manager.create_enhanced_stream(
            teacher_id, session_id, None, quality
        )

        teacher_token = livekit_manager.generate_access_token(
            room_name=session_id,
            participant_identity=teacher_id,
            participant_name=teacher_name,
            is_teacher=True
        )

        # Dynamically generate WebSocket URL
        ws_scheme = 'wss' if request.scheme == 'https' else 'ws'
        stream_url = f"{ws_scheme}://{request.host}/socket.io/"

        return jsonify({
            'message': 'Enhanced stream with LiveKit started successfully',
            'session_id': session_id,
            'stream_url': stream_url,
            'livekit_url': LIVEKIT_URL,
            'livekit_token': teacher_token,
            'features': {
                'chat': True,
                'recording': True,
                'quality_controls': True,
                'screen_sharing': True,
                'livekit_enabled': True
            },
            'quality_settings': quality_settings[session_id]
        }), 200
    except Exception as e:
        print(f"Enhanced stream start error: {e}")
        return jsonify({'message': 'Failed to start enhanced stream'}), 500

@app.route('/api/enhanced-stream/refresh-token', methods=['POST'])
def refresh_livekit_token():
    """Refresh LiveKit access token for long streaming sessions"""
    try:
        data = request.get_json()
        session_id = data.get('session_id')
        room_name = data.get('room_name', session_id)

        if not session_id:
            return jsonify({'message': 'session_id is required'}), 400

        # Get the stream to verify it exists
        stream = enhanced_stream_manager.get_stream(session_id)
        if not stream:
            return jsonify({'message': 'Stream session not found'}), 404

        # Generate new token for the teacher
        teacher_id = stream.get('teacher_id', 'demo_teacher')
        teacher_name = stream.get('teacher_name', teacher_id)

        new_token = livekit_manager.generate_access_token(
            room_name=room_name,
            participant_identity=teacher_id,
            participant_name=teacher_name,
            is_teacher=True
        )

        print(f"✅ Token refreshed for session {session_id}")

        return jsonify({
            'message': 'Token refreshed successfully',
            'livekit_token': new_token,
            'session_id': session_id,
            'room_name': room_name
        }), 200

    except Exception as e:
        print(f"❌ Token refresh error: {e}")
        return jsonify({'message': 'Failed to refresh token', 'error': str(e)}), 500

@app.route('/api/livekit/token', methods=['POST'])
def generate_livekit_token():
    """Generate LiveKit access token for participants"""
    try:
        data = request.get_json()
        room_name = data.get('room_name') or data.get('session_id')
        participant_id = data.get('participant_id') or data.get('user_id')
        participant_name = data.get('participant_name', participant_id)
        is_teacher = data.get('is_teacher', False)

        if not room_name or not participant_id:
            return jsonify({'message': 'room_name and participant_id are required'}), 400

        stream = enhanced_stream_manager.get_stream(room_name)
        if not stream:
            return jsonify({'message': 'Stream not found'}), 404

        token = livekit_manager.generate_access_token(
            room_name=room_name,
            participant_identity=participant_id,
            participant_name=participant_name,
            is_teacher=is_teacher
        )

        if token:
            return jsonify({
                'token': token,
                'livekit_url': LIVEKIT_URL,
                'room_name': room_name,
                'participant_id': participant_id,
                'is_teacher': is_teacher
            }), 200
        else:
            return jsonify({'message': 'Failed to generate token'}), 500
    except Exception as e:
        print(f"Token generation error: {e}")
        return jsonify({'message': 'Failed to generate token'}), 500

@app.route('/api/livekit/join', methods=['POST'])
def join_livekit_room():
    """Join LiveKit room and get connection details"""
    try:
        data = request.get_json()
        session_id = data.get('session_id')
        user_id = data.get('user_id')
        user_name = data.get('user_name', user_id)
        user_role = data.get('user_role', 'student')

        if not session_id or not user_id:
            return jsonify({'message': 'session_id and user_id are required'}), 400

        stream = enhanced_stream_manager.get_stream(session_id)
        if not stream:
            return jsonify({'message': 'Stream not found'}), 404

        is_teacher = (user_role in ['faculty', 'kota_teacher'] or user_id == stream.get('teacher_id'))
        token = livekit_manager.generate_access_token(
            room_name=session_id,
            participant_identity=user_id,
            participant_name=user_name,
            is_teacher=is_teacher
        )

        if token:
            return jsonify({
                'success': True,
                'livekit_url': LIVEKIT_URL,
                'token': token,
                'room_name': session_id,
                'participant_id': user_id,
                'participant_name': user_name,
                'is_teacher': is_teacher,
                'stream_info': {
                    'session_id': session_id,
                    'teacher_id': stream['teacher_id'],
                    'viewer_count': stream['viewer_count'],
                    'quality': stream['quality'],
                    'features': {
                        'chat_enabled': stream['chat_enabled'],
                        'recording_enabled': stream['recording_enabled'],
                        'screen_sharing': stream['screen_sharing']
                    }
                }
            }), 200
        else:
            return jsonify({'message': 'Failed to generate access token'}), 500
    except Exception as e:
        print(f"LiveKit join error: {e}")
        return jsonify({'message': 'Failed to join LiveKit room'}), 500

@app.route('/api/enhanced-stream/stop', methods=['POST'])
def stop_enhanced_stream():
    """Stop enhanced streaming session"""
    try:
        data = request.get_json() or {}
        session_id = data.get('session_id')
        teacher_id = data.get('teacher_id')

        print(f"🛑 Received stop request for session: {session_id}, teacher: {teacher_id}")
        if not session_id and teacher_id:
            for sid, stream in enhanced_stream_manager.streams.items():
                if stream.get('teacher_id') == teacher_id and stream.get('status') == 'active':
                    session_id = sid
                    print(f"🔍 Found active stream for teacher {teacher_id}: {session_id}")
                    break

        if not session_id:
            active_sessions = list(enhanced_stream_manager.streams.keys())
            if active_sessions:
                print(f"⚠ No session_id provided, stopping all active streams: {active_sessions}")
                stopped_count = 0
                for sid in active_sessions:
                    if enhanced_stream_manager.stop_stream(sid):
                        socketio.emit('stream_ended', {
                            'session_id': sid,
                            'message': 'Stream has ended'
                        }, room=sid)
                        stopped_count += 1
                print(f"✅ Stopped {stopped_count} streams and notified all viewers")
                return jsonify({
                    'message': f'Stopped {stopped_count} active streams',
                    'stopped_sessions': active_sessions,
                    'recording_saved': True
                }), 200
            else:
                return jsonify({'message': 'No active streams found'}), 404

        success = enhanced_stream_manager.stop_stream(session_id)
        if success:
            socketio.emit('stream_ended', {
                'session_id': session_id,
                'message': 'Stream has ended'
            }, room=session_id)
            print(f"✅ Successfully stopped stream {session_id} and notified viewers")
            return jsonify({
                'message': 'Enhanced stream stopped successfully',
                'session_id': session_id,
                'recording_saved': True
            }), 200
        else:
            print(f"❌ Stream {session_id} not found")
            return jsonify({'message': 'Stream not found'}), 404
    except Exception as e:
        print(f"❌ Enhanced stream stop error: {e}")
        return jsonify({'message': 'Failed to stop enhanced stream', 'error': str(e)}), 500

@app.route('/api/enhanced-stream/status/<session_id>', methods=['GET'])
def get_enhanced_stream_status(session_id):
    """Get enhanced stream status"""
    stream = enhanced_stream_manager.get_stream(session_id)
    if stream:
        return jsonify({
            'session_id': session_id,
            'status': stream['status'],
            'viewer_count': stream['viewer_count'],
            'quality': stream['quality'],
            'features': {
                'chat_enabled': stream['chat_enabled'],
                'recording_enabled': stream['recording_enabled'],
                'screen_sharing': stream['screen_sharing']
            },
            'uptime': (datetime.now(timezone.utc) - stream['created_at']).total_seconds()
        }), 200
    else:
        return jsonify({'message': 'Stream not found'}), 404

@app.route('/active-streams', methods=['GET'])
def get_active_streams():
    """Get all active enhanced streams"""
    try:
        active_streams = []
        for session_id, stream in enhanced_stream_manager.streams.items():
            if stream['status'] == 'active':
                stream_info = {
                    'session_id': session_id,
                    'teacher_id': stream['teacher_id'],
                    'viewer_count': stream['viewer_count'],
                    'quality': stream['quality'],
                    'created_at': stream['created_at'].isoformat(),
                    'uptime': (datetime.now(timezone.utc) - stream['created_at']).total_seconds(),
                    'features': {
                        'chat_enabled': stream['chat_enabled'],
                        'recording_enabled': stream['recording_enabled'],
                        'screen_sharing': stream['screen_sharing']
                    }
                }
                active_streams.append(stream_info)
        return jsonify({
            'success': True,
            'streams': active_streams,
            'active_streams': active_streams,
            'total_count': len(active_streams),
            'service': 'Enhanced Real-time Streaming',
            'timestamp': datetime.now(timezone.utc).isoformat()
        }), 200
    except Exception as e:
        print(f"❌ Error getting active streams: {e}")
        return jsonify({'message': 'Failed to get active streams', 'error': str(e)}), 500

@app.route('/api/enhanced-stream/list', methods=['GET'])
def list_enhanced_streams():
    """List all enhanced streams (active and inactive)"""
    try:
        all_streams = []
        for session_id, stream in enhanced_stream_manager.streams.items():
            stream_info = {
                'session_id': session_id,
                'teacher_id': stream['teacher_id'],
                'status': stream['status'],
                'viewer_count': stream['viewer_count'],
                'quality': stream['quality'],
                'created_at': stream['created_at'].isoformat(),
                'uptime': (datetime.now(timezone.utc) - stream['created_at']).total_seconds(),
                'features': {
                    'chat_enabled': stream['chat_enabled'],
                    'recording_enabled': stream['recording_enabled'],
                    'screen_sharing': stream['screen_sharing']
                }
            }
            all_streams.append(stream_info)
        for session_id, recording in enhanced_stream_manager.recordings.items():
            recording_info = {
                'session_id': session_id,
                'teacher_id': recording['teacher_id'],
                'status': 'recorded',
                'duration': recording['duration'],
                'quality': recording['quality'],
                'viewer_count': recording['viewer_count'],
                'chat_messages': recording['chat_messages'],
                'recorded_at': recording['recorded_at'].isoformat()
            }
            all_streams.append(recording_info)
        return jsonify({
            'streams': all_streams,
            'total_count': len(all_streams),
            'active_count': len([s for s in all_streams if s['status'] == 'active']),
            'recorded_count': len([s for s in all_streams if s['status'] == 'recorded']),
            'service': 'Enhanced Real-time Streaming',
            'timestamp': datetime.now(timezone.utc).isoformat()
        }), 200
    except Exception as e:
        print(f"❌ Error listing streams: {e}")
        return jsonify({'message': 'Failed to list streams', 'error': str(e)}), 500

# RTMP Ingress API Endpoints
@app.route('/api/rtmp-ingress/test', methods=['GET'])
def test_rtmp_ingress():
    """Test RTMP ingress configuration"""
    try:
        # Test environment variables first
        config_status = {
            'livekit_url': LIVEKIT_URL is not None,
            'livekit_api_key': LIVEKIT_API_KEY is not None,
            'livekit_api_secret': LIVEKIT_API_SECRET is not None,
            'livekit_url_value': LIVEKIT_URL,
            'livekit_api_key_value': LIVEKIT_API_KEY[:10] + '...' if LIVEKIT_API_KEY else None
        }

        # Test LiveKit API connection
        try:
            lkapi = get_livekit_api()
            api_status = 'initialized successfully'
        except Exception as api_error:
            api_status = f'failed: {str(api_error)}'

        return jsonify({
            'success': True,
            'message': 'RTMP ingress configuration test',
            'config': config_status,
            'api_status': api_status
        }), 200

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'RTMP ingress test failed: {str(e)}',
            'error_type': str(type(e))
        }), 500

@app.route('/api/rtmp-ingress/create', methods=['POST'])
def create_rtmp_ingress_endpoint():
    """Create RTMP ingress for streaming session"""
    try:
        print(f"🔄 Starting RTMP ingress creation...")

        # Validate request data
        data = request.get_json()
        if not data:
            return jsonify({'success': False, 'message': 'No JSON data provided'}), 400

        session_id = data.get('session_id', str(uuid.uuid4()))
        teacher_id = data.get('teacher_id', 'demo_teacher')
        teacher_name = data.get('teacher_name', teacher_id)

        print(f"🔄 Creating RTMP ingress for session: {session_id}, teacher: {teacher_id}")

        # Validate environment variables
        if not all([LIVEKIT_URL, LIVEKIT_API_KEY, LIVEKIT_API_SECRET]):
            return jsonify({
                'success': False,
                'message': 'LiveKit configuration missing'
            }), 500

        # Create RTMP ingress using REST API
        ingress_info = rtmp_ingress_manager.create_rtmp_ingress_rest(session_id, teacher_id, teacher_name)

        if ingress_info:
            print(f"✅ Successfully created RTMP ingress for session {session_id}")
            return jsonify({
                'success': True,
                'session_id': session_id,
                'rtmp_url': ingress_info.url,
                'stream_key': ingress_info.stream_key,
                'ingress_id': ingress_info.ingress_id,
                'livekit_url': LIVEKIT_URL,
                'participant_identity': teacher_id,
                'participant_name': teacher_name
            }), 200
        else:
            print(f"❌ Failed to create RTMP ingress for session {session_id}")
            return jsonify({'success': False, 'message': 'Failed to create RTMP ingress'}), 500

    except RecursionError as e:
        print(f"❌ Recursion error in RTMP ingress creation: {e}")
        return jsonify({
            'success': False,
            'message': 'Recursion error occurred',
            'error_type': 'RecursionError'
        }), 500
    except Exception as e:
        print(f"❌ RTMP ingress creation error: {e}")
        print(f"❌ Error type: {type(e)}")
        print(f"❌ Error details: {str(e)}")
        import traceback
        print(f"❌ Full traceback: {traceback.format_exc()}")
        return jsonify({
            'success': False,
            'message': f'Failed to create RTMP ingress',
            'error_type': str(type(e))
        }), 500

@app.route('/api/rtmp-ingress/delete', methods=['POST'])
def delete_rtmp_ingress():
    """Delete RTMP ingress for streaming session"""
    try:
        data = request.get_json()
        session_id = data.get('session_id')

        if not session_id:
            return jsonify({'success': False, 'message': 'Session ID required'}), 400

        # Delete RTMP ingress using REST API
        success = rtmp_ingress_manager.delete_rtmp_ingress_rest(session_id)

        if success:
            return jsonify({
                'success': True,
                'message': f'RTMP ingress deleted for session {session_id}'
            }), 200
        else:
            return jsonify({'success': False, 'message': 'Failed to delete RTMP ingress'}), 500

    except Exception as e:
        print(f"❌ RTMP ingress deletion error: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/rtmp-ingress/status/<session_id>', methods=['GET'])
def get_rtmp_ingress_status(session_id):
    """Get RTMP ingress status for a session"""
    try:
        ingress_info = rtmp_ingress_manager.get_ingress_info(session_id)

        if ingress_info:
            return jsonify({
                'success': True,
                'session_id': session_id,
                'ingress_id': ingress_info['ingress_id'],
                'rtmp_url': ingress_info['rtmp_url'],
                'stream_key': ingress_info['stream_key'],
                'teacher_id': ingress_info['teacher_id'],
                'teacher_name': ingress_info['teacher_name'],
                'status': ingress_info['status'],
                'created_at': ingress_info['created_at'].isoformat()
            }), 200
        else:
            return jsonify({'success': False, 'message': 'RTMP ingress not found'}), 404

    except Exception as e:
        print(f"❌ RTMP ingress status error: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/rtmp-ingress/list', methods=['GET'])
def list_rtmp_ingresses():
    """List all RTMP ingresses"""
    try:
        # Get local ingress sessions
        local_sessions = []
        for session_id, ingress_info in rtmp_ingress_sessions.items():
            local_sessions.append({
                'session_id': session_id,
                'ingress_id': ingress_info['ingress_id'],
                'rtmp_url': ingress_info['rtmp_url'],
                'stream_key': ingress_info['stream_key'],
                'teacher_id': ingress_info['teacher_id'],
                'teacher_name': ingress_info['teacher_name'],
                'status': ingress_info['status'],
                'created_at': ingress_info['created_at'].isoformat()
            })

        return jsonify({
            'success': True,
            'ingresses': local_sessions,
            'total_count': len(local_sessions)
        }), 200

    except Exception as e:
        print(f"❌ RTMP ingress list error: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

# Socket.IO Events
@socketio.on('connect')
def handle_connect():
    print(f"✅ Enhanced client connected: {request.sid}")
    emit('connected', {'message': 'Connected to enhanced streaming service'})

@socketio.on('disconnect')
def handle_disconnect():
    print(f"❌ Enhanced client disconnected: {request.sid}")
    for session_id in list(enhanced_stream_manager.streams.keys()):
        enhanced_stream_manager.remove_viewer(session_id, request.sid)

@socketio.on('start_stream')
def handle_start_stream(data):
    try:
        session_id = data.get('session_id')
        teacher_id = data.get('teacher_id')
        teacher_name = data.get('teacher_name', teacher_id)
        quality = data.get('quality', 'medium')

        if not session_id or not teacher_id:
            emit('error', {'message': 'Session ID and Teacher ID required'})
            return

        stream = enhanced_stream_manager.create_enhanced_stream(
            teacher_id, session_id, request.sid, quality
        )
        join_room(session_id)
        teacher_token = livekit_manager.generate_access_token(
            room_name=session_id,
            participant_identity=teacher_id,
            participant_name=teacher_name,
            is_teacher=True
        )
        emit('stream_started', {
            'session_id': session_id,
            'message': 'Stream started successfully with LiveKit',
            'livekit_url': LIVEKIT_URL,
            'livekit_token': teacher_token,
            'features': {
                'chat': True,
                'recording': True,
                'quality_controls': True,
                'screen_sharing': True,
                'livekit_enabled': True
            }
        })
        print(f"🎬 Teacher {teacher_id} started LiveKit stream {session_id}")
    except Exception as e:
        print(f"❌ Error starting stream: {e}")
        emit('error', {'message': f'Failed to start stream: {str(e)}'})

@socketio.on('stop_stream')
def handle_stop_stream(data):
    try:
        session_id = data.get('session_id')
        teacher_id = data.get('teacher_id')
        print(f"🛑 Socket stop_stream request: session={session_id}, teacher={teacher_id}")

        if not session_id and teacher_id:
            for sid, stream in enhanced_stream_manager.streams.items():
                if stream.get('teacher_id') == teacher_id and stream.get('status') == 'active':
                    session_id = sid
                    print(f"🔍 Found active stream for teacher {teacher_id}: {session_id}")
                    break

        if not session_id:
            emit('error', {'message': 'Session ID required or no active stream found'})
            return

        success = enhanced_stream_manager.stop_stream(session_id)
        if success:
            socketio.emit('stream_ended', {
                'session_id': session_id,
                'message': 'Stream has ended'
            }, room=session_id)
            leave_room(session_id)
            emit('stream_stopped', {
                'session_id': session_id,
                'message': 'Stream stopped successfully'
            })
            print(f"✅ Teacher {teacher_id} stopped stream {session_id}")
        else:
            emit('error', {'message': 'Failed to stop stream - stream not found'})
    except Exception as e:
        print(f"❌ Error stopping stream: {e}")
        emit('error', {'message': f'Failed to stop stream: {str(e)}'})

@socketio.on('end_stream')
def handle_end_stream(data):
    try:
        session_id = data.get('session_id')
        teacher_id = data.get('teacher_id')
        print(f"🔄 Received end_stream event for session {session_id}, teacher {teacher_id}")
        print(f"🔄 Forwarding to stop_stream handler for backward compatibility")
        handle_stop_stream(data)
    except Exception as e:
        print(f"❌ Error handling end_stream: {e}")
        emit('error', {'message': f'Failed to end stream: {str(e)}'})

@socketio.on('join_stream')
def handle_join_stream(data):
    try:
        session_id = data.get('session_id')
        faculty_id = data.get('faculty_id') or data.get('viewer_id') or data.get('user_id')
        user_name = data.get('user_name') or data.get('viewer_name') or faculty_id
        user_role = data.get('user_role', 'student')

        if not session_id:
            emit('error', {'message': 'Session ID required'})
            return

        stream = enhanced_stream_manager.get_stream(session_id)
        if not stream:
            emit('error', {'message': 'Stream not found'})
            return

        join_room(session_id)
        viewer_data = {
            'viewer_id': faculty_id,
            'viewer_name': user_name,
            'user_role': user_role,
            'socket_id': request.sid,
            'joined_at': datetime.now(timezone.utc)
        }
        if 'viewer_details' not in stream:
            stream['viewer_details'] = {}
        stream['viewer_details'][faculty_id] = viewer_data

        success = enhanced_stream_manager.add_viewer(session_id, faculty_id, request.sid)
        if success:
            is_teacher = (user_role in ['faculty', 'kota_teacher'] or faculty_id == stream.get('teacher_id'))
            viewer_token = livekit_manager.generate_access_token(
                room_name=session_id,
                participant_identity=faculty_id,
                participant_name=user_name,
                is_teacher=is_teacher
            )
            emit('stream_joined', {
                'session_id': session_id,
                'viewer_count': stream['viewer_count'],
                'message': 'Successfully joined stream',
                'livekit_url': LIVEKIT_URL,
                'livekit_token': viewer_token,
                'is_teacher': is_teacher,
                'stream_info': {
                    'teacher_id': stream['teacher_id'],
                    'quality': stream['quality'],
                    'features': {
                        'chat_enabled': stream['chat_enabled'],
                        'recording_enabled': stream['recording_enabled'],
                        'screen_sharing': stream['screen_sharing']
                    }
                }
            })
            emit('viewer_joined', {
                'viewer_id': faculty_id,
                'viewer_name': user_name,
                'viewer_count': stream['viewer_count'],
                'is_teacher': is_teacher,
                'user_role': user_role
            }, room=session_id, include_self=False)
            print(f"👥 Viewer {faculty_id} joined LiveKit stream {session_id}")
        else:
            emit('error', {'message': 'Failed to join stream'})
    except Exception as e:
        print(f"❌ Error joining stream: {e}")
        emit('error', {'message': f'Failed to join stream: {str(e)}'})

@socketio.on('leave_stream')
def handle_leave_stream(data):
    try:
        session_id = data.get('session_id')
        faculty_id = data.get('faculty_id') or data.get('viewer_id')
        if session_id:
            leave_room(session_id)
            enhanced_stream_manager.remove_viewer(session_id, request.sid)
            stream = enhanced_stream_manager.get_stream(session_id)
            if stream:
                if 'viewer_details' in stream and faculty_id in stream['viewer_details']:
                    del stream['viewer_details'][faculty_id]
                emit('viewer_left', {
                    'viewer_id': faculty_id,
                    'viewer_count': stream['viewer_count']
                }, room=session_id)
                print(f"👋 Viewer {faculty_id} left stream {session_id}")
    except Exception as e:
        print(f"❌ Error leaving stream: {e}")

@socketio.on('join_enhanced_stream')
def handle_join_enhanced_stream(data):
    try:
        session_id = data.get('session_id')
        viewer_id = data.get('viewer_id')
        viewer_name = data.get('viewer_name', viewer_id)
        user_role = data.get('user_role', 'student')

        if not session_id:
            emit('error', {'message': 'Session ID required'})
            return

        join_room(session_id)
        success = enhanced_stream_manager.add_viewer(session_id, viewer_id, request.sid)
        if success:
            stream = enhanced_stream_manager.get_stream(session_id)
            is_teacher = (user_role in ['faculty', 'kota_teacher'] or viewer_id == stream.get('teacher_id'))
            viewer_token = livekit_manager.generate_access_token(
                room_name=session_id,
                participant_identity=viewer_id,
                participant_name=viewer_name,
                is_teacher=is_teacher
            )
            emit('joined_enhanced_stream', {
                'session_id': session_id,
                'viewer_count': stream['viewer_count'],
                'quality_settings': quality_settings.get(session_id, {}),
                'chat_history': chat_messages.get(session_id, [])[-50:],
                'livekit_url': LIVEKIT_URL,
                'livekit_token': viewer_token,
                'is_teacher': is_teacher
            })
            emit('viewer_joined', {
                'viewer_id': viewer_id,
                'viewer_name': viewer_name,
                'viewer_count': stream['viewer_count'],
                'is_teacher': is_teacher
            }, room=session_id, include_self=False)
        else:
            emit('error', {'message': 'Failed to join stream'})
    except Exception as e:
        print(f"❌ Error joining enhanced stream: {e}")
        emit('error', {'message': 'Failed to join stream'})

@socketio.on('request_livekit_token')
def handle_request_livekit_token(data):
    try:
        session_id = data.get('session_id')
        user_id = data.get('user_id')
        user_name = data.get('user_name', user_id)
        user_role = data.get('user_role', 'student')

        if not session_id or not user_id:
            emit('error', {'message': 'Session ID and User ID required'})
            return

        stream = enhanced_stream_manager.get_stream(session_id)
        if not stream:
            emit('error', {'message': 'Stream not found'})
            return

        is_teacher = (user_role in ['faculty', 'kota_teacher'] or user_id == stream.get('teacher_id'))
        token = livekit_manager.generate_access_token(
            room_name=session_id,
            participant_identity=user_id,
            participant_name=user_name,
            is_teacher=is_teacher
        )
        if token:
            emit('livekit_token_generated', {
                'session_id': session_id,
                'livekit_url': LIVEKIT_URL,
                'livekit_token': token,
                'participant_id': user_id,
                'participant_name': user_name,
                'is_teacher': is_teacher
            })
        else:
            emit('error', {'message': 'Failed to generate LiveKit token'})
    except Exception as e:
        print(f"❌ Error generating LiveKit token: {e}")
        emit('error', {'message': 'Failed to generate token'})

@socketio.on('leave_enhanced_stream')
def handle_leave_enhanced_stream(data):
    try:
        session_id = data.get('session_id')
        if session_id:
            leave_room(session_id)
            enhanced_stream_manager.remove_viewer(session_id, request.sid)
            stream = enhanced_stream_manager.get_stream(session_id)
            if stream:
                emit('viewer_left', {
                    'viewer_count': stream['viewer_count']
                }, room=session_id)
    except Exception as e:
        print(f"❌ Error leaving enhanced stream: {e}")

@socketio.on('enhanced_video_frame')
def handle_enhanced_video_frame(data):
    try:
        session_id = data.get('session_id')
        frame_data = data.get('frame_data')
        quality = data.get('quality', 'medium')
        if not session_id or not frame_data:
            return
        if session_id in quality_settings:
            quality_settings[session_id]['video_quality'] = quality
        socketio.emit('enhanced_video_frame', {
            'session_id': session_id,
            'frame_data': frame_data,
            'quality': quality,
            'timestamp': datetime.now(timezone.utc).isoformat()
        }, room=session_id, include_self=False)
    except Exception as e:
        print(f"❌ Error handling enhanced video frame: {e}")

@socketio.on('chat_message')
def handle_chat_message(data):
    try:
        session_id = data.get('session_id')
        message = data.get('message')
        sender_id = data.get('sender_id')
        sender_name = data.get('sender_name', 'Anonymous')
        if not session_id or not message:
            return
        sender_role = get_user_role_by_id(sender_id) or 'unknown'
        if session_id not in chat_messages:
            chat_messages[session_id] = []
        chat_data = {
            'id': str(uuid.uuid4()),
            'session_id': session_id,
            'sender_id': sender_id,
            'sender_name': sender_name,
            'sender_role': sender_role,
            'message': message,
            'timestamp': datetime.now(timezone.utc).isoformat()
        }
        chat_messages[session_id].append(chat_data)
        socketio.emit('chat_message', chat_data, room=session_id)
        print(f"💬 Chat message from {sender_name} ({sender_role}) in session {session_id}: {message}")
    except Exception as e:
        print(f"❌ Error handling chat message: {e}")

@socketio.on('get_chat_history')
def handle_get_chat_history(data):
    try:
        session_id = data.get('session_id')
        if session_id and session_id in chat_messages:
            history = chat_messages[session_id][-50:]
            emit('chat_history', {
                'session_id': session_id,
                'messages': history
            })
        else:
            emit('chat_history', {
                'session_id': session_id,
                'messages': []
            })
    except Exception as e:
        print(f"❌ Error getting chat history: {e}")

@socketio.on('video_frame')
def handle_video_frame_legacy(data):
    try:
        session_id = data.get('session_id')
        frame_data = data.get('frame_data')
        frame_type = data.get('frame_type', 'camera')
        if not session_id or not frame_data:
            return
        socketio.emit('video_frame', {
            'session_id': session_id,
            'frame_data': frame_data,
            'frame_type': frame_type,
            'timestamp': datetime.now(timezone.utc).isoformat()
        }, room=session_id, include_self=False)
    except Exception as e:
        print(f"❌ Error handling video frame: {e}")

@socketio.on('screen_frame')
def handle_screen_frame_legacy(data):
    try:
        session_id = data.get('session_id')
        frame_data = data.get('frame_data')
        if not session_id or not frame_data:
            return
        socketio.emit('screen_frame', {
            'session_id': session_id,
            'frame_data': frame_data,
            'timestamp': datetime.now(timezone.utc).isoformat()
        }, room=session_id, include_self=False)
    except Exception as e:
        print(f"❌ Error handling screen frame: {e}")

# RTMP Relay Socket.IO Events
@socketio.on('rtmp_start')
def handle_rtmp_start(data):
    """Handle RTMP relay start request"""
    try:
        session_id = data.get('sessionId')
        rtmp_url = data.get('rtmpUrl')
        stream_key = data.get('streamKey')

        if not all([session_id, rtmp_url, stream_key]):
            emit('rtmp_error', {'error': 'Missing required parameters'})
            return

        # Start RTMP relay
        success = rtmp_relay_manager.start_rtmp_relay(session_id, rtmp_url, stream_key)

        if success:
            emit('rtmp_started', {'sessionId': session_id})
            print(f"✅ RTMP relay started for session {session_id}")
        else:
            emit('rtmp_error', {'error': 'Failed to start RTMP relay'})

    except Exception as e:
        print(f"❌ Error starting RTMP relay: {e}")
        emit('rtmp_error', {'error': str(e)})

@socketio.on('rtmp_stop')
def handle_rtmp_stop(data):
    """Handle RTMP relay stop request"""
    try:
        session_id = data.get('sessionId')

        if not session_id:
            emit('rtmp_error', {'error': 'Session ID required'})
            return

        # Stop RTMP relay
        success = rtmp_relay_manager.stop_rtmp_relay(session_id)

        if success:
            emit('rtmp_stopped', {'sessionId': session_id})
            print(f"✅ RTMP relay stopped for session {session_id}")
        else:
            emit('rtmp_error', {'error': 'Failed to stop RTMP relay'})

    except Exception as e:
        print(f"❌ Error stopping RTMP relay: {e}")
        emit('rtmp_error', {'error': str(e)})

@socketio.on('rtmp_media_data')
def handle_rtmp_media_data(data):
    """Handle media data for RTMP relay"""
    try:
        session_id = data.get('sessionId')
        media_data = data.get('mediaData')

        if not session_id or not media_data:
            return

        # Convert base64 media data to bytes if needed
        if isinstance(media_data, str):
            media_data = base64.b64decode(media_data)

        # Add media data to relay queue
        rtmp_relay_manager.add_media_data(session_id, media_data)

    except Exception as e:
        print(f"❌ Error handling RTMP media data: {e}")

@socketio.on('ping')
def handle_ping():
    """Handle ping for connection keepalive"""
    emit('pong')

if __name__ == '__main__':
    print("🚀 Starting Enhanced Real-time Streaming Service with LiveKit...")
    print("🎯 Features: LiveKit + WebRTC + Socket.IO + Chat + Recording + Quality Controls")
    print("🌐 CORS enabled for all origins with full preflight support")
    print("🔧 Socket.IO CORS configured with credentials support")
    print(f"🎥 LiveKit URL: {LIVEKIT_URL}")
    print(f"🔑 LiveKit API Key: {LIVEKIT_API_KEY}")
    print("🚀 Server starting on port 8012...")
    print("=" * 70)

    if not all([LIVEKIT_URL, LIVEKIT_API_KEY, LIVEKIT_API_SECRET]):
        print("❌ LiveKit configuration missing! Please check your .env file.")
        print("Required: LIVEKIT_URL, LIVEKIT_API_KEY, LIVEKIT_API_SECRET")
        exit(1)

    print("✅ LiveKit configuration validated")
    print("🎬 Ready to create streaming rooms with LiveKit integration")

    # Initialize LiveKit async system
    print("🔄 Initializing LiveKit async system...")
    init_livekit_async()
    print("✅ LiveKit async system initialized")

    DEBUG = os.getenv('FLASK_DEBUG', 'False').lower() in ['true', '1', 't']

    # For production deployment, use allow_unsafe_werkzeug=True
    # Note: For better production performance, consider using Gunicorn
    socketio.run(app, host='0.0.0.0', port=8012, debug=DEBUG, allow_unsafe_werkzeug=True)